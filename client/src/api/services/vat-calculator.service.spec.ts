import { VatCalculatorService } from './vat-calculator.service';
import Stripe from 'stripe';

describe('VatCalculatorService', () => {
  // Helper function to create mock tax rate
  const createMockTaxRate = (percentage: number, taxType: string, displayName: string): Stripe.TaxRate => ({
    id: `txr_${taxType}`,
    object: 'tax_rate',
    active: true,
    country: 'BG',
    created: 1234567890,
    description: null,
    display_name: displayName,
    effective_percentage: null,
    flat_amount: null,
    inclusive: false,
    jurisdiction: null,
    jurisdiction_level: null,
    livemode: false,
    metadata: null,
    percentage,
    rate_type: null,
    state: null,
    tax_type: taxType as any
  });

  // Helper function to create mock tax breakdown item
  const createMockTax = (
    amount: number,
    percentage: number,
    taxType: string,
    displayName: string,
    taxabilityReason: string | null = null
  ): Stripe.Checkout.Session.TotalDetails.Breakdown.Tax => ({
    amount,
    rate: createMockTaxRate(percentage, taxType, displayName),
    taxability_reason: taxabilityReason as any,
    taxable_amount: amount * 100 / percentage
  });
  describe('calculateVatInfoFromStripeSession', () => {
    it('should use tax_exempt field when available', () => {
      const session = {
        customer_details: {
          tax_exempt: 'reverse'
        },
        total_details: {
          breakdown: {
            taxes: []
          }
        }
      } as any;

      const result = VatCalculatorService.calculateVatInfoFromStripeSession(session);

      expect(result.percent).toBe(0);
      expect(result.reason_without).toBe('чл.86, ал.3 и чл.21 от ЗДДС');
    });

    it('should fallback to breakdown analysis when tax_exempt is not available', () => {
      const session = {
        customer_details: null,
        total_details: {
          breakdown: {
            taxes: [createMockTax(2000, 20, 'vat', 'VAT')]
          }
        }
      } as any;

      const result = VatCalculatorService.calculateVatInfoFromStripeSession(session);

      expect(result.percent).toBe(20);
      expect(result.reason_without).toBeNull();
    });

    it('should handle missing customer_details', () => {
      const session = {
        total_details: {
          breakdown: {
            taxes: []
          }
        }
      } as any;

      const result = VatCalculatorService.calculateVatInfoFromStripeSession(session);

      expect(result.percent).toBe(0);
      expect(result.reason_without).toBe('чл.113 ал.9 от ЗДДС');
    });
  });

  describe('calculateVatInfoFromTaxExempt', () => {
    it('should handle reverse charge correctly', () => {
      const result = VatCalculatorService.calculateVatInfoFromTaxExempt('reverse', []);

      expect(result.percent).toBe(0);
      expect(result.reason_without).toBe('чл.86, ал.3 и чл.21 от ЗДДС');
    });

    it('should handle exempt status correctly', () => {
      const result = VatCalculatorService.calculateVatInfoFromTaxExempt('exempt', []);

      expect(result.percent).toBe(0);
      expect(result.reason_without).toBe('чл.21 от ЗДДС');
    });

    it('should extract VAT percentage when tax_exempt is none and VAT is applied', () => {
      const taxBreakdown = [createMockTax(2000, 20, 'vat', 'VAT')];
      const result = VatCalculatorService.calculateVatInfoFromTaxExempt('none', taxBreakdown);

      expect(result.percent).toBe(20);
      expect(result.reason_without).toBeNull();
    });

    it('should handle none status with no VAT in breakdown', () => {
      const taxBreakdown = [createMockTax(500, 5, 'sales_tax', 'Sales Tax')];
      const result = VatCalculatorService.calculateVatInfoFromTaxExempt('none', taxBreakdown);

      expect(result.percent).toBe(0);
      expect(result.reason_without).toBe('чл.113 ал.9 от ЗДДС');
    });

    it('should fallback to breakdown analysis for unknown tax_exempt values', () => {
      const taxBreakdown = [createMockTax(2000, 20, 'vat', 'VAT')];
      const result = VatCalculatorService.calculateVatInfoFromTaxExempt('unknown', taxBreakdown);

      expect(result.percent).toBe(20);
      expect(result.reason_without).toBeNull();
    });
  });

  describe('calculateVatInfoFromStripeBreakdown', () => {
    it('should return 0% VAT with exemption reason when no tax breakdown provided', () => {
      const result = VatCalculatorService.calculateVatInfoFromStripeBreakdown([]);

      expect(result.percent).toBe(0);
      expect(result.reason_without).toBe('чл.113 ал.9 от ЗДДС');
    });

    it('should extract VAT percentage from Stripe tax breakdown', () => {
      const taxBreakdown = [
        createMockTax(2000, 20, 'vat', 'VAT')
      ];

      const result = VatCalculatorService.calculateVatInfoFromStripeBreakdown(taxBreakdown);

      expect(result.percent).toBe(20);
      expect(result.reason_without).toBeNull();
    });

    it('should handle multiple tax types and find VAT', () => {
      const taxBreakdown = [
        createMockTax(500, 5, 'sales_tax', 'Sales Tax'),
        createMockTax(2000, 20, 'vat', 'VAT')
      ];

      const result = VatCalculatorService.calculateVatInfoFromStripeBreakdown(taxBreakdown);

      expect(result.percent).toBe(20);
      expect(result.reason_without).toBeNull();
    });

    it('should return exemption reason when no VAT found in breakdown', () => {
      const taxBreakdown = [
        createMockTax(500, 5, 'sales_tax', 'Sales Tax')
      ];

      const result = VatCalculatorService.calculateVatInfoFromStripeBreakdown(taxBreakdown);

      expect(result.percent).toBe(0);
      expect(result.reason_without).toBe('чл.113 ал.9 от ЗДДС');
    });

    it('should handle different VAT percentages', () => {
      const taxBreakdown = [
        createMockTax(1900, 19, 'vat', 'Reduced VAT')
      ];

      const result = VatCalculatorService.calculateVatInfoFromStripeBreakdown(taxBreakdown);

      expect(result.percent).toBe(19);
      expect(result.reason_without).toBeNull();
    });

    it('should map reverse_charge to Bulgarian reverse charge exemption', () => {
      const taxBreakdown = [
        createMockTax(0, 20, 'vat', 'VAT', 'reverse_charge')
      ];

      const result = VatCalculatorService.calculateVatInfoFromStripeBreakdown(taxBreakdown);

      expect(result.percent).toBe(0);
      expect(result.reason_without).toBe('чл.86, ал.3 и чл.21 от ЗДДС');
    });

    it('should map zero_rated to EU VAT exemption', () => {
      const taxBreakdown = [
        createMockTax(0, 0, 'vat', 'VAT', 'zero_rated')
      ];

      const result = VatCalculatorService.calculateVatInfoFromStripeBreakdown(taxBreakdown);

      expect(result.percent).toBe(0);
      expect(result.reason_without).toBe('чл.21 ал.2 от ЗДДС');
    });

    it('should map customer_exempt to international exemption', () => {
      const taxBreakdown = [
        createMockTax(0, 0, 'vat', 'VAT', 'customer_exempt')
      ];

      const result = VatCalculatorService.calculateVatInfoFromStripeBreakdown(taxBreakdown);

      expect(result.percent).toBe(0);
      expect(result.reason_without).toBe('чл.21 от ЗДДС');
    });

    it('should map not_collecting to domestic without VAT exemption', () => {
      const taxBreakdown = [
        createMockTax(0, 0, 'vat', 'VAT', 'not_collecting')
      ];

      const result = VatCalculatorService.calculateVatInfoFromStripeBreakdown(taxBreakdown);

      expect(result.percent).toBe(0);
      expect(result.reason_without).toBe('чл.82, ал. 2 от ЗДДС');
    });

    it('should find exemption reason from non-VAT tax entries', () => {
      const taxBreakdown = [
        createMockTax(0, 0, 'sales_tax', 'Sales Tax', 'reverse_charge')
      ];

      const result = VatCalculatorService.calculateVatInfoFromStripeBreakdown(taxBreakdown);

      expect(result.percent).toBe(0);
      expect(result.reason_without).toBe('чл.86, ал.3 и чл.21 от ЗДДС');
    });
  });

  describe('isValidExemptionReason', () => {
    it('should return true for valid exemption reasons', () => {
      const validReasons = [
        "чл.113 ал.9 от ЗДДС",
        "чл.86, ал.3 и чл.21 от ЗДДС",
        "чл.82, ал. 2 от ЗДДС",
        "чл.21 от ЗДДС",
        "чл.21 ал.2 от ЗДДС"
      ];

      validReasons.forEach(reason => {
        expect(VatCalculatorService.isValidExemptionReason(reason)).toBe(true);
      });
    });

    it('should return false for invalid exemption reasons', () => {
      expect(VatCalculatorService.isValidExemptionReason('Invalid reason')).toBe(false);
    });
  });

  describe('formatVatPercent', () => {
    it('should format decimal to percentage number', () => {
      expect(VatCalculatorService.formatVatPercent(0.20)).toBe(20);
      expect(VatCalculatorService.formatVatPercent(0.19)).toBe(19);
      expect(VatCalculatorService.formatVatPercent(0)).toBe(0);
    });
  });

  describe('parseVatPercent', () => {
    it('should parse percentage number to decimal', () => {
      expect(VatCalculatorService.parseVatPercent(20)).toBe(0.20);
      expect(VatCalculatorService.parseVatPercent(19.5)).toBe(0.195);
      expect(VatCalculatorService.parseVatPercent(0)).toBe(0);
    });
  });
});
